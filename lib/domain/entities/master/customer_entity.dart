import 'package:equatable/equatable.dart';

class CustomerEntity extends Equatable {
  final int? id;
  final String name;
  final String address;
  final String contactPerson;
  final String mobileNo;
  final String email;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const CustomerEntity({
    this.id,
    required this.name,
    required this.address,
    required this.contact<PERSON><PERSON>,
    required this.mobileNo,
    required this.email,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        address,
        contactPerson,
        mobileNo,
        email,
        createdAt,
        updatedAt,
      ];
}
