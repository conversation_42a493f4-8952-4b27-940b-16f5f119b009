import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/job_allocation/job_allocation_entity.dart';
import '../../repositories/job_allocation_repository.dart';

class GetJobAllocationsUseCase {
  final JobAllocationRepository repository;

  GetJobAllocationsUseCase(this.repository);

  Future<Either<Failure, List<JobAllocationEntity>>> call() async {
    return await repository.getJobAllocations();
  }
}
