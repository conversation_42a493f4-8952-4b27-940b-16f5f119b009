import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/job_allocation/job_allocation_entity.dart';

abstract class JobAllocationRepository {
  Future<Either<Failure, List<JobAllocationEntity>>> getJobAllocations();
  
  Future<Either<Failure, JobAllocationEntity>> getJobAllocationById(int id);
  
  Future<Either<Failure, JobAllocationEntity>> createJobAllocation(
    JobAllocationEntity jobAllocation,
  );
  
  Future<Either<Failure, JobAllocationEntity>> updateJobAllocation(
    int id,
    JobAllocationEntity jobAllocation,
  );
  
  Future<Either<Failure, void>> deleteJobAllocation(int id);
}
