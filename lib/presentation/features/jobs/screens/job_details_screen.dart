import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../core/router/app_routes.dart';

class JobDetailsScreen extends StatefulWidget {
  final int jobId;

  const JobDetailsScreen({
    super.key,
    required this.jobId,
  });

  @override
  State<JobDetailsScreen> createState() => _JobDetailsScreenState();
}

class _JobDetailsScreenState extends State<JobDetailsScreen> {
  bool _isLoading = true;
  late Map<String, dynamic> _jobData;

  @override
  void initState() {
    super.initState();
    _loadJobData();
  }

  void _loadJobData() {
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _jobData = {
          'id': widget.jobId,
          'jobNumber': 'JOB-2024-001',
          'testRequestNumber': 'TR-2024-001',
          'customerName': 'ABC Industries',
          'customerAddress': '123 Industrial Ave, City, State 12345',
          'contactPerson': '<PERSON>',
          'mobileNo': '******-0123',
          'email': '<EMAIL>',
          'assignedTo': 'John Analyst',
          'assignedDate': '2024-01-15',
          'dueDate': '2024-01-20',
          'status': 'in_progress',
          'priority': 'high',
          'sampleType': 'Water',
          'sampleCode': 'WTR-001',
          'quantityOfSample': '500ml',
          'sampleDetails': 'Drinking water sample from main supply',
          'specialRequest': 'Rush analysis required',
          'createdBy': 'Chief Chemist',
          'createdDate': '2024-01-15',
          'progress': 60,
          'parameters': [
            {
              'id': 1,
              'name': 'pH Level',
              'units': 'pH units',
              'permissibleLimit': '6.5-8.5',
              'method': 'Electrometric',
              'result': '7.2',
              'status': 'completed',
              'remarks': 'Standard procedure followed',
            },
            {
              'id': 2,
              'name': 'Chlorine Content',
              'units': 'mg/L',
              'permissibleLimit': '0.2-1.0',
              'method': 'Colorimetric',
              'result': '',
              'status': 'pending',
              'remarks': '',
            },
            {
              'id': 3,
              'name': 'Turbidity',
              'units': 'NTU',
              'permissibleLimit': '< 5',
              'method': 'Nephelometric',
              'result': '',
              'status': 'pending',
              'remarks': '',
            },
          ],
          'instructions': 'Follow standard testing procedures. Ensure proper calibration before testing.',
        };
        _isLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Job Details'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_jobData['jobNumber']),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _editJob();
                  break;
                case 'reassign':
                  _reassignJob();
                  break;
                case 'delete':
                  _deleteJob();
                  break;
                case 'history':
                  _showHistory();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('Edit Job'),
                ),
              ),
              const PopupMenuItem(
                value: 'reassign',
                child: ListTile(
                  leading: Icon(Icons.person_add),
                  title: Text('Reassign'),
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete),
                  title: Text('Delete Job'),
                ),
              ),
              const PopupMenuItem(
                value: 'history',
                child: ListTile(
                  leading: Icon(Icons.history),
                  title: Text('View History'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status and Progress Card
            _buildStatusCard(),
            const SizedBox(height: 16),

            // Job Information
            _buildJobInfoCard(),
            const SizedBox(height: 16),

            // Customer Information
            _buildCustomerInfoCard(),
            const SizedBox(height: 16),

            // Sample Information
            _buildSampleInfoCard(),
            const SizedBox(height: 16),

            // Test Parameters
            _buildParametersCard(),
            const SizedBox(height: 16),

            // Instructions
            if (_jobData['instructions'] != null) _buildInstructionsCard(),
          ],
        ),
      ),
      floatingActionButton: _jobData['status'] == 'assigned' || _jobData['status'] == 'in_progress'
          ? FloatingActionButton.extended(
              onPressed: () {
                context.push(AppRoutes.fillResultsWithId(_jobData['id']));
              },
              icon: const Icon(Icons.edit),
              label: Text(_jobData['status'] == 'assigned' ? 'Start Job' : 'Continue'),
            )
          : null,
    );
  }

  Widget _buildStatusCard() {
    final progress = _jobData['progress'] as int;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: _getStatusColor(_jobData['status']),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Status: ${_getStatusLabel(_jobData['status'])}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: _getStatusColor(_jobData['status']),
                      ),
                    ),
                  ],
                ),
                _buildPriorityChip(_jobData['priority']),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  'Progress: $progress%',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: LinearProgressIndicator(
                    value: progress / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      progress == 100 ? Colors.green : Colors.blue,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.work, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Job Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Test Request', _jobData['testRequestNumber']),
            _buildInfoRow('Assigned To', _jobData['assignedTo']),
            _buildInfoRow('Assigned Date', _jobData['assignedDate']),
            _buildInfoRow('Due Date', _jobData['dueDate']),
            _buildInfoRow('Created By', _jobData['createdBy']),
            _buildInfoRow('Created Date', _jobData['createdDate']),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.business, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Customer Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Customer Name', _jobData['customerName']),
            _buildInfoRow('Address', _jobData['customerAddress']),
            _buildInfoRow('Contact Person', _jobData['contactPerson']),
            _buildInfoRow('Mobile', _jobData['mobileNo']),
            _buildInfoRow('Email', _jobData['email']),
          ],
        ),
      ),
    );
  }

  Widget _buildSampleInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.science, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Sample Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Sample Type', _jobData['sampleType']),
            _buildInfoRow('Sample Code', _jobData['sampleCode']),
            _buildInfoRow('Quantity', _jobData['quantityOfSample']),
            _buildInfoRow('Details', _jobData['sampleDetails']),
            if (_jobData['specialRequest'] != null)
              _buildInfoRow('Special Request', _jobData['specialRequest']),
          ],
        ),
      ),
    );
  }

  Widget _buildParametersCard() {
    final parameters = _jobData['parameters'] as List<dynamic>;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.assignment, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Test Parameters',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...parameters.map((param) => _buildParameterItem(param)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildParameterItem(Map<String, dynamic> param) {
    final isCompleted = param['status'] == 'completed';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(
          color: isCompleted ? Colors.green.shade300 : Colors.grey.shade300,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isCompleted ? Colors.green.shade50 : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                param['name'],
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isCompleted ? Colors.green : Colors.orange,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  isCompleted ? 'Completed' : 'Pending',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Method: ${param['method']}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          Text(
            'Limit: ${param['permissibleLimit']} ${param['units']}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          if (isCompleted && param['result'].isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Result: ${param['result']} ${param['units']}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (param['remarks'].isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Remarks: ${param['remarks']}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInstructionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.note, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Instructions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _jobData['instructions'],
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    IconData icon;
    
    switch (priority) {
      case 'high':
        color = Colors.red;
        icon = Icons.keyboard_arrow_up;
        break;
      case 'medium':
        color = Colors.orange;
        icon = Icons.remove;
        break;
      case 'low':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        break;
      default:
        color = Colors.grey;
        icon = Icons.remove;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            priority.toUpperCase(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'assigned':
        return Colors.blue;
      case 'in_progress':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusLabel(String status) {
    switch (status) {
      case 'assigned':
        return 'Assigned';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  void _editJob() {
    // TODO: Navigate to edit job screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit job functionality coming soon')),
    );
  }

  void _reassignJob() {
    // TODO: Show reassignment dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Reassign job functionality coming soon')),
    );
  }

  void _deleteJob() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Job'),
        content: const Text('Are you sure you want to delete this job? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Job deleted')),
              );
              context.pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showHistory() {
    // TODO: Show job history
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Job history functionality coming soon')),
    );
  }
}
