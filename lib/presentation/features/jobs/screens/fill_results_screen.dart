import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/widgets/error_text_field.dart';
import '../../../../core/error/failures.dart';

class FillResultsScreen extends StatefulWidget {
  final int jobId;

  const FillResultsScreen({
    super.key,
    required this.jobId,
  });

  @override
  State<FillResultsScreen> createState() => _FillResultsScreenState();
}

class _FillResultsScreenState extends State<FillResultsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _commentsController = TextEditingController();
  
  bool _isLoading = false;
  bool _dataLoading = true;
  bool _isSaving = false;
  Failure? _fillResultsFailure;
  
  late Map<String, dynamic> _jobData;
  final Map<int, TextEditingController> _resultControllers = {};
  final Map<int, TextEditingController> _remarksControllers = {};

  @override
  void initState() {
    super.initState();
    _loadJobData();
  }

  void _loadJobData() {
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _jobData = {
          'id': widget.jobId,
          'jobNumber': 'JOB-2024-001',
          'testRequestNumber': 'TR-2024-001',
          'customerName': 'ABC Industries',
          'sampleType': 'Water',
          'sampleCode': 'WTR-001',
          'assignedDate': '2024-01-15',
          'dueDate': '2024-01-20',
          'priority': 'high',
          'status': 'in_progress',
          'progress': 60,
          'parameters': [
            {
              'id': 1,
              'name': 'pH Level',
              'units': 'pH units',
              'permissibleLimit': '6.5-8.5',
              'method': 'Electrometric',
              'result': '7.2',
              'remarks': 'Standard procedure followed',
              'status': 'completed',
            },
            {
              'id': 2,
              'name': 'Chlorine Content',
              'units': 'mg/L',
              'permissibleLimit': '0.2-1.0',
              'method': 'Colorimetric',
              'result': '',
              'remarks': '',
              'status': 'pending',
            },
            {
              'id': 3,
              'name': 'Turbidity',
              'units': 'NTU',
              'permissibleLimit': '< 5',
              'method': 'Nephelometric',
              'result': '',
              'remarks': '',
              'status': 'pending',
            },
          ],
          'qualityChecks': {
            'calibrationVerified': true,
            'duplicateAnalysis': false,
            'blankAnalysis': false,
            'standardVerification': false,
          },
          'analystComments': 'pH test completed successfully. Proceeding with remaining tests.',
        };
        
        // Initialize controllers with existing data
        for (var param in _jobData['parameters']) {
          _resultControllers[param['id']] = TextEditingController(text: param['result']);
          _remarksControllers[param['id']] = TextEditingController(text: param['remarks']);
        }
        _commentsController.text = _jobData['analystComments'];
        
        _dataLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_dataLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Fill Results'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final completedTests = (_jobData['parameters'] as List)
        .where((p) => p['status'] == 'completed').length;
    final totalTests = (_jobData['parameters'] as List).length;
    final progress = totalTests > 0 ? (completedTests / totalTests * 100).round() : 0;

    return Scaffold(
      appBar: AppBar(
        title: Text(_jobData['jobNumber']),
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _saveProgress,
            child: _isSaving
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Error Summary
              ValidationErrorSummary(failure: _fillResultsFailure),

              // Job Information
              _buildJobInfoCard(),
              const SizedBox(height: 16),

              // Progress Card
              _buildProgressCard(progress, completedTests, totalTests),
              const SizedBox(height: 16),

              // Test Parameters
              _buildParametersSection(),
              const SizedBox(height: 16),

              // Quality Checks
              _buildQualityChecksSection(),
              const SizedBox(height: 16),

              // Analyst Comments
              _buildCommentsSection(),
              const SizedBox(height: 24),

              // Action Buttons
              _buildActionButtons(progress),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildJobInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Job Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Test Request', _jobData['testRequestNumber']),
            _buildInfoRow('Customer', _jobData['customerName']),
            _buildInfoRow('Sample Type', _jobData['sampleType']),
            _buildInfoRow('Sample Code', _jobData['sampleCode']),
            _buildInfoRow('Due Date', _jobData['dueDate']),
            Row(
              children: [
                const SizedBox(width: 120, child: Text('Priority:')),
                _buildPriorityChip(_jobData['priority']),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressCard(int progress, int completed, int total) {
    return Card(
      color: progress == 100 ? Colors.green.shade50 : Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  progress == 100 ? Icons.check_circle : Icons.hourglass_empty,
                  color: progress == 100 ? Colors.green : Colors.blue,
                ),
                const SizedBox(width: 8),
                Text(
                  'Progress: $progress%',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: progress == 100 ? Colors.green.shade700 : Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: progress / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                progress == 100 ? Colors.green : Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '$completed of $total tests completed',
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParametersSection() {
    final parameters = _jobData['parameters'] as List;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.science, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Test Parameters',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...parameters.map((param) => _buildParameterCard(param)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildParameterCard(Map<String, dynamic> param) {
    final isCompleted = param['status'] == 'completed';
    final controller = _resultControllers[param['id']]!;
    final remarksController = _remarksControllers[param['id']]!;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(
          color: isCompleted ? Colors.green.shade300 : Colors.grey.shade300,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isCompleted ? Colors.green.shade50 : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                param['name'],
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isCompleted ? Colors.green : Colors.orange,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  isCompleted ? 'Completed' : 'Pending',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Method: ${param['method']}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          Text(
            'Limit: ${param['permissibleLimit']} ${param['units']}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          const SizedBox(height: 12),
          
          // Result Input
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: controller,
                  decoration: InputDecoration(
                    labelText: 'Result',
                    suffixText: param['units'],
                    border: const OutlineInputBorder(),
                    enabled: !isCompleted,
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter result';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              if (!isCompleted)
                ElevatedButton.icon(
                  onPressed: () => _markParameterComplete(param),
                  icon: const Icon(Icons.check, size: 16),
                  label: const Text('Complete'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Remarks Input
          TextFormField(
            controller: remarksController,
            decoration: const InputDecoration(
              labelText: 'Remarks (Optional)',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
            enabled: !isCompleted,
          ),
        ],
      ),
    );
  }

  Widget _buildQualityChecksSection() {
    final checks = _jobData['qualityChecks'] as Map<String, dynamic>;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.verified, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Quality Checks',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...checks.entries.map((entry) => CheckboxListTile(
              title: Text(_formatCheckName(entry.key)),
              value: entry.value,
              onChanged: (value) {
                setState(() {
                  checks[entry.key] = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            )).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.comment, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Analyst Comments',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ErrorTextField(
              fieldName: 'comments',
              labelText: 'Comments',
              controller: _commentsController,
              failure: _fillResultsFailure,
              maxLines: 4,
              prefixIcon: const Icon(Icons.note),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(int progress) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _saveProgress,
            icon: const Icon(Icons.save),
            label: const Text('Save Progress'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: progress == 100 ? _submitResults : null,
            icon: const Icon(Icons.send),
            label: const Text('Submit Results'),
            style: ElevatedButton.styleFrom(
              backgroundColor: progress == 100 ? Colors.green : null,
              foregroundColor: progress == 100 ? Colors.white : null,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    IconData icon;
    
    switch (priority) {
      case 'high':
        color = Colors.red;
        icon = Icons.keyboard_arrow_up;
        break;
      case 'medium':
        color = Colors.orange;
        icon = Icons.remove;
        break;
      case 'low':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        break;
      default:
        color = Colors.grey;
        icon = Icons.remove;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            priority.toUpperCase(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  String _formatCheckName(String key) {
    return key.split(RegExp(r'(?=[A-Z])'))
        .map((word) => word.isNotEmpty 
            ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
            : word)
        .join(' ');
  }

  void _markParameterComplete(Map<String, dynamic> param) {
    final controller = _resultControllers[param['id']]!;
    
    if (controller.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a result value')),
      );
      return;
    }

    setState(() {
      param['status'] = 'completed';
      param['result'] = controller.text.trim();
      param['remarks'] = _remarksControllers[param['id']]!.text.trim();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${param['name']} marked as complete')),
    );
  }

  void _saveProgress() async {
    setState(() {
      _isSaving = true;
      _fillResultsFailure = null;
    });

    try {
      // TODO: Implement actual save with BLoC
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Progress saved successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _fillResultsFailure = const ValidationFailure(
          message: 'Failed to save progress',
        );
      });
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _submitResults() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check if all parameters are completed
    final parameters = _jobData['parameters'] as List;
    final incompleteParams = parameters.where((p) => p['status'] != 'completed').toList();
    
    if (incompleteParams.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please complete all test parameters')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Submit Results'),
        content: const Text('Are you sure you want to submit the test results? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              
              setState(() {
                _isLoading = true;
              });

              try {
                // TODO: Implement actual submission with BLoC
                await Future.delayed(const Duration(seconds: 2)); // Simulate API call
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Results submitted successfully')),
                  );
                  context.pop();
                }
              } catch (e) {
                setState(() {
                  _fillResultsFailure = const ValidationFailure(
                    message: 'Failed to submit results',
                  );
                });
              } finally {
                if (mounted) {
                  setState(() {
                    _isLoading = false;
                  });
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Submit'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _commentsController.dispose();
    for (var controller in _resultControllers.values) {
      controller.dispose();
    }
    for (var controller in _remarksControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }
}
