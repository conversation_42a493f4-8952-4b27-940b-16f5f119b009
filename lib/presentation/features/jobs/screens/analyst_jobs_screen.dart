import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../core/router/app_routes.dart';

class AnalystJobsScreen extends StatefulWidget {
  const AnalystJobsScreen({super.key});

  @override
  State<AnalystJobsScreen> createState() => _AnalystJobsScreenState();
}

class _AnalystJobsScreenState extends State<AnalystJobsScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _statusFilter = 'all';

  // Mock data - will be replaced with actual data from BLoC
  final List<Map<String, dynamic>> _assignedJobs = [
    {
      'id': 1,
      'jobNumber': 'JOB-2024-001',
      'testRequestNumber': 'TR-2024-001',
      'customerName': 'ABC Industries',
      'assignedDate': '2024-01-15',
      'dueDate': '2024-01-20',
      'status': 'assigned',
      'priority': 'high',
      'testCount': 3,
      'sampleType': 'Water',
      'sampleCode': 'WTR-001',
      'parameters': ['pH Level', 'Chlorine Content', 'Turbidity'],
      'progress': 0,
      'isOverdue': false,
    },
    {
      'id': 2,
      'jobNumber': 'JOB-2024-002',
      'testRequestNumber': 'TR-2024-002',
      'customerName': 'XYZ Corp',
      'assignedDate': '2024-01-16',
      'dueDate': '2024-01-22',
      'status': 'in_progress',
      'priority': 'medium',
      'testCount': 5,
      'sampleType': 'Soil',
      'sampleCode': 'SOL-002',
      'parameters': ['Heavy Metals', 'pH', 'Organic Content', 'Moisture', 'Density'],
      'progress': 60,
      'isOverdue': false,
    },
    {
      'id': 3,
      'jobNumber': 'JOB-2024-003',
      'testRequestNumber': 'TR-2024-003',
      'customerName': 'DEF Ltd',
      'assignedDate': '2024-01-14',
      'dueDate': '2024-01-19',
      'status': 'completed',
      'priority': 'low',
      'testCount': 2,
      'sampleType': 'Air',
      'sampleCode': 'AIR-003',
      'parameters': ['Particulate Matter', 'Gas Composition'],
      'progress': 100,
      'completedDate': '2024-01-18',
      'isOverdue': false,
    },
    {
      'id': 4,
      'jobNumber': 'JOB-2024-004',
      'testRequestNumber': 'TR-2024-004',
      'customerName': 'GHI Corp',
      'assignedDate': '2024-01-10',
      'dueDate': '2024-01-18',
      'status': 'assigned',
      'priority': 'high',
      'testCount': 4,
      'sampleType': 'Water',
      'sampleCode': 'WTR-004',
      'parameters': ['Bacteria Count', 'pH', 'Chlorine', 'Hardness'],
      'progress': 0,
      'isOverdue': true,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredJobs = _getFilteredJobs();
    final overdueJobs = filteredJobs.where((job) => job['isOverdue'] == true).length;

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Jobs'),
        actions: [
          if (overdueJobs > 0)
            Container(
              margin: const EdgeInsets.only(right: 16),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '$overdueJobs Overdue',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[50],
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search by job number, customer, or sample...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 12),

                // Status Filter
                Row(
                  children: [
                    const Text('Status: '),
                    const SizedBox(width: 8),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _statusFilter,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('All')),
                          DropdownMenuItem(value: 'assigned', child: Text('Assigned')),
                          DropdownMenuItem(value: 'in_progress', child: Text('In Progress')),
                          DropdownMenuItem(value: 'completed', child: Text('Completed')),
                          DropdownMenuItem(value: 'overdue', child: Text('Overdue')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _statusFilter = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Jobs List
          Expanded(
            child: filteredJobs.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: filteredJobs.length,
                    itemBuilder: (context, index) {
                      final job = filteredJobs[index];
                      return _buildJobCard(job);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildJobCard(Map<String, dynamic> job) {
    final isOverdue = job['isOverdue'] == true;
    final progress = job['progress'] as int;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          context.push(AppRoutes.fillResultsWithId(job['id']));
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: isOverdue ? Border.all(color: Colors.red, width: 2) : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      job['jobNumber'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      children: [
                        if (isOverdue) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'OVERDUE',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                        _buildPriorityChip(job['priority']),
                        const SizedBox(width: 8),
                        _buildStatusChip(job['status']),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Test Request: ${job['testRequestNumber']}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  job['customerName'],
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.science, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${job['sampleType']} - ${job['sampleCode']}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(Icons.assignment, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${job['testCount']} tests',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Due: ${job['dueDate']}',
                      style: TextStyle(
                        fontSize: 12,
                        color: isOverdue ? Colors.red : Colors.grey[600],
                        fontWeight: isOverdue ? FontWeight.w500 : FontWeight.normal,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Assigned: ${job['assignedDate']}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),

                // Progress Bar
                if (job['status'] != 'assigned') ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Text(
                        'Progress: $progress%',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: LinearProgressIndicator(
                          value: progress / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            progress == 100 ? Colors.green : Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],

                // Parameters Preview
                const SizedBox(height: 12),
                Text(
                  'Parameters: ${(job['parameters'] as List).take(3).join(', ')}${(job['parameters'] as List).length > 3 ? '...' : ''}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                  ),
                ),

                // Action buttons
                if (job['status'] != 'completed') ...[
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (job['status'] == 'assigned') ...[
                        OutlinedButton.icon(
                          onPressed: () => _rejectJob(job),
                          icon: const Icon(Icons.close, size: 16),
                          label: const Text('Reject'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red,
                            side: const BorderSide(color: Colors.red),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: () => context.push(AppRoutes.fillResultsWithId(job['id'])),
                          icon: const Icon(Icons.play_arrow, size: 16),
                          label: const Text('Start'),
                        ),
                      ] else ...[
                        ElevatedButton.icon(
                          onPressed: () => context.push(AppRoutes.fillResultsWithId(job['id'])),
                          icon: const Icon(Icons.edit, size: 16),
                          label: const Text('Continue'),
                        ),
                      ],
                    ],
                  ),
                ] else if (job['completedDate'] != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.check_circle, size: 16, color: Colors.green[600]),
                      const SizedBox(width: 4),
                      Text(
                        'Completed on ${job['completedDate']}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;

    switch (status) {
      case 'assigned':
        color = Colors.blue;
        label = 'Assigned';
        break;
      case 'in_progress':
        color = Colors.orange;
        label = 'In Progress';
        break;
      case 'completed':
        color = Colors.green;
        label = 'Completed';
        break;
      default:
        color = Colors.grey;
        label = 'Unknown';
    }

    return Chip(
      label: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      ),
      backgroundColor: color,
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    IconData icon;

    switch (priority) {
      case 'high':
        color = Colors.red;
        icon = Icons.keyboard_arrow_up;
        break;
      case 'medium':
        color = Colors.orange;
        icon = Icons.remove;
        break;
      case 'low':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        break;
      default:
        color = Colors.grey;
        icon = Icons.remove;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 2),
          Text(
            priority.toUpperCase(),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.work_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No jobs assigned',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Jobs assigned to you will appear here',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredJobs() {
    return _assignedJobs.where((job) {
      final matchesSearch = _searchQuery.isEmpty ||
          job['jobNumber'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
          job['testRequestNumber'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
          job['customerName'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
          job['sampleCode'].toLowerCase().contains(_searchQuery.toLowerCase());

      bool matchesStatus = false;
      if (_statusFilter == 'all') {
        matchesStatus = true;
      } else if (_statusFilter == 'overdue') {
        matchesStatus = job['isOverdue'] == true;
      } else {
        matchesStatus = job['status'] == _statusFilter;
      }

      return matchesSearch && matchesStatus;
    }).toList();
  }

  void _rejectJob(Map<String, dynamic> job) {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reject Job ${job['jobNumber']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejecting this job:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Rejection Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.trim().isNotEmpty) {
                Navigator.of(context).pop();
                setState(() {
                  _assignedJobs.removeWhere((j) => j['id'] == job['id']);
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Job ${job['jobNumber']} rejected')),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}