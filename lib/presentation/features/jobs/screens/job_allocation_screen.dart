import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/router/app_routes.dart';

class JobAllocationScreen extends StatefulWidget {
  const JobAllocationScreen({super.key});

  @override
  State<JobAllocationScreen> createState() => _JobAllocationScreenState();
}

class _JobAllocationScreenState extends State<JobAllocationScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _statusFilter = 'all';

  // Mock data - will be replaced with actual data from BLoC
  final List<Map<String, dynamic>> _mockJobs = [
    {
      'id': 1,
      'jobNumber': 'JOB-2024-001',
      'testRequestNumber': 'TR-2024-001',
      'customerName': 'ABC Industries',
      'assignedTo': 'John Analyst',
      'assignedDate': '2024-01-15',
      'dueDate': '2024-01-20',
      'status': 'assigned',
      'priority': 'high',
      'testCount': 3,
      'sampleType': 'Water',
    },
    {
      'id': 2,
      'jobNumber': 'JOB-2024-002',
      'testRequestNumber': 'TR-2024-002',
      'customerName': 'XYZ Corp',
      'assignedTo': null,
      'assignedDate': null,
      'dueDate': '2024-01-22',
      'status': 'pending',
      'priority': 'medium',
      'testCount': 5,
      'sampleType': 'Soil',
    },
    {
      'id': 3,
      'jobNumber': 'JOB-2024-003',
      'testRequestNumber': 'TR-2024-003',
      'customerName': 'DEF Ltd',
      'assignedTo': 'Jane Analyst',
      'assignedDate': '2024-01-16',
      'dueDate': '2024-01-21',
      'status': 'in_progress',
      'priority': 'low',
      'testCount': 2,
      'sampleType': 'Air',
    },
  ];

  final List<Map<String, dynamic>> _analysts = [
    {'name': 'John Analyst', 'id': 1, 'activeJobs': 2},
    {'name': 'Jane Analyst', 'id': 2, 'activeJobs': 1},
    {'name': 'Mike Technician', 'id': 3, 'activeJobs': 3},
  ];

  @override
  Widget build(BuildContext context) {
    final filteredJobs = _getFilteredJobs();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Job Allocation'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.push(AppRoutes.createJob);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.grey[50],
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search by job number, test request, or customer...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 12),

                // Status Filter
                Row(
                  children: [
                    const Text('Status: '),
                    const SizedBox(width: 8),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _statusFilter,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('All')),
                          DropdownMenuItem(value: 'pending', child: Text('Pending Assignment')),
                          DropdownMenuItem(value: 'assigned', child: Text('Assigned')),
                          DropdownMenuItem(value: 'in_progress', child: Text('In Progress')),
                          DropdownMenuItem(value: 'completed', child: Text('Completed')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _statusFilter = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Jobs List
          Expanded(
            child: filteredJobs.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: filteredJobs.length,
                    itemBuilder: (context, index) {
                      final job = filteredJobs[index];
                      return _buildJobCard(job);
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push(AppRoutes.createJob);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildJobCard(Map<String, dynamic> job) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          context.push(AppRoutes.jobDetailsWithId(job['id']));
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    job['jobNumber'],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      _buildPriorityChip(job['priority']),
                      const SizedBox(width: 8),
                      _buildStatusChip(job['status']),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Test Request: ${job['testRequestNumber']}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                job['customerName'],
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    job['assignedTo'] ?? 'Unassigned',
                    style: TextStyle(
                      fontSize: 12,
                      color: job['assignedTo'] != null ? Colors.grey[600] : Colors.red,
                      fontWeight: job['assignedTo'] != null ? FontWeight.normal : FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Due: ${job['dueDate']}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.science, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    job['sampleType'],
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.assignment, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${job['testCount']} tests',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              if (job['status'] == 'pending') ...[
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _assignJob(job),
                      icon: const Icon(Icons.person_add, size: 16),
                      label: const Text('Assign'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;

    switch (status) {
      case 'pending':
        color = Colors.orange;
        label = 'Pending';
        break;
      case 'assigned':
        color = Colors.blue;
        label = 'Assigned';
        break;
      case 'in_progress':
        color = Colors.purple;
        label = 'In Progress';
        break;
      case 'completed':
        color = Colors.green;
        label = 'Completed';
        break;
      default:
        color = Colors.grey;
        label = 'Unknown';
    }

    return Chip(
      label: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      ),
      backgroundColor: color,
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    IconData icon;

    switch (priority) {
      case 'high':
        color = Colors.red;
        icon = Icons.keyboard_arrow_up;
        break;
      case 'medium':
        color = Colors.orange;
        icon = Icons.remove;
        break;
      case 'low':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        break;
      default:
        color = Colors.grey;
        icon = Icons.remove;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 2),
          Text(
            priority.toUpperCase(),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.work_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No jobs found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first job allocation',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              context.push(AppRoutes.createJob);
            },
            icon: const Icon(Icons.add),
            label: const Text('Create Job'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredJobs() {
    return _mockJobs.where((job) {
      final matchesSearch = _searchQuery.isEmpty ||
          job['jobNumber'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
          job['testRequestNumber'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
          job['customerName'].toLowerCase().contains(_searchQuery.toLowerCase());

      final matchesStatus = _statusFilter == 'all' || job['status'] == _statusFilter;

      return matchesSearch && matchesStatus;
    }).toList();
  }

  void _assignJob(Map<String, dynamic> job) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Assign Job ${job['jobNumber']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Select an analyst to assign this job:'),
            const SizedBox(height: 16),
            ..._analysts.map((analyst) => ListTile(
              leading: CircleAvatar(
                child: Text(analyst['name'].split(' ').map((n) => n[0]).join()),
              ),
              title: Text(analyst['name']),
              subtitle: Text('${analyst['activeJobs']} active jobs'),
              onTap: () {
                Navigator.of(context).pop();
                setState(() {
                  job['assignedTo'] = analyst['name'];
                  job['status'] = 'assigned';
                  job['assignedDate'] = DateTime.now().toString().split(' ')[0];
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Job ${job['jobNumber']} assigned to ${analyst['name']}')),
                );
              },
            )).toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}