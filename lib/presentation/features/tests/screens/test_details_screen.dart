import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class TestDetailsScreen extends StatefulWidget {
  final int testRequestId;

  const TestDetailsScreen({
    super.key,
    required this.testRequestId,
  });

  @override
  State<TestDetailsScreen> createState() => _TestDetailsScreenState();
}

class _TestDetailsScreenState extends State<TestDetailsScreen> {
  // Mock data - will be replaced with actual data from BLoC
  late Map<String, dynamic> _testRequest;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTestRequest();
  }

  void _loadTestRequest() {
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _testRequest = {
          'id': widget.testRequestId,
          'requestNumber': 'TR-2024-001',
          'requestDate': '2024-01-15',
          'customerName': 'ABC Industries',
          'customerAddress': '123 Industrial Ave, City, State 12345',
          'contactPerson': '<PERSON>',
          'mobileNo': '******-0123',
          'email': '<EMAIL>',
          'status': 'pending',
          'sampleType': 'Water',
          'sampleCode': 'WTR-001',
          'quantityOfSample': '500ml',
          'sampleDetails': 'Drinking water sample from main supply',
          'submittedByName': 'Jane Doe',
          'submittedByDesignation': 'Quality Manager',
          'submittedByDate': '2024-01-15',
          'receivedByName': 'Lab Technician',
          'receivedByDesignation': 'Senior Technician',
          'receivedByDate': '2024-01-15',
          'specialRequest': 'Rush analysis required',
          'samples': [
            {
              'id': 1,
              'parameterId': 1,
              'particulars': 'pH Level',
              'type': 'Chemical',
              'quantity': '100ml',
              'remarks': 'Standard test',
              'parameter': {
                'name': 'pH Level',
                'units': 'pH units',
                'permissibleLimit': '6.5-8.5',
              }
            },
            {
              'id': 2,
              'parameterId': 2,
              'particulars': 'Chlorine Content',
              'type': 'Chemical',
              'quantity': '200ml',
              'remarks': 'Free chlorine',
              'parameter': {
                'name': 'Chlorine Content',
                'units': 'mg/L',
                'permissibleLimit': '0.2-1.0',
              }
            },
          ],
        };
        _isLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Test Details'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_testRequest['requestNumber']),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  // TODO: Navigate to edit screen
                  break;
                case 'delete':
                  _showDeleteConfirmation();
                  break;
                case 'download':
                  _downloadReport();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('Edit'),
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete),
                  title: Text('Delete'),
                ),
              ),
              const PopupMenuItem(
                value: 'download',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Download Report'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: _getStatusColor(_testRequest['status']),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Status: ${_getStatusLabel(_testRequest['status'])}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: _getStatusColor(_testRequest['status']),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Customer Information
            _buildSectionCard(
              'Customer Information',
              Icons.business,
              [
                _buildInfoRow('Customer Name', _testRequest['customerName']),
                _buildInfoRow('Address', _testRequest['customerAddress']),
                _buildInfoRow('Contact Person', _testRequest['contactPerson']),
                _buildInfoRow('Mobile', _testRequest['mobileNo']),
                _buildInfoRow('Email', _testRequest['email']),
              ],
            ),
            const SizedBox(height: 16),

            // Sample Information
            _buildSectionCard(
              'Sample Information',
              Icons.science,
              [
                _buildInfoRow('Sample Type', _testRequest['sampleType']),
                _buildInfoRow('Sample Code', _testRequest['sampleCode']),
                _buildInfoRow('Quantity', _testRequest['quantityOfSample']),
                _buildInfoRow('Details', _testRequest['sampleDetails']),
                if (_testRequest['specialRequest'] != null)
                  _buildInfoRow('Special Request', _testRequest['specialRequest']),
              ],
            ),
            const SizedBox(height: 16),

            // Submission Information
            _buildSectionCard(
              'Submission Information',
              Icons.person,
              [
                _buildInfoRow('Submitted By', _testRequest['submittedByName']),
                _buildInfoRow('Designation', _testRequest['submittedByDesignation']),
                _buildInfoRow('Submitted Date', _testRequest['submittedByDate']),
                _buildInfoRow('Received By', _testRequest['receivedByName']),
                _buildInfoRow('Received Date', _testRequest['receivedByDate']),
              ],
            ),
            const SizedBox(height: 16),

            // Test Parameters
            _buildTestParametersSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, IconData icon, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestParametersSection() {
    final samples = _testRequest['samples'] as List<dynamic>;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.assignment, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Test Parameters',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...samples.map((sample) => _buildParameterCard(sample)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildParameterCard(Map<String, dynamic> sample) {
    final parameter = sample['parameter'];
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            parameter['name'],
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Type: ${sample['type']}',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ),
              Expanded(
                child: Text(
                  'Quantity: ${sample['quantity']}',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Units: ${parameter['units']}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          Text(
            'Limit: ${parameter['permissibleLimit']}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          if (sample['remarks'] != null && sample['remarks'].isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'Remarks: ${sample['remarks']}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'in_progress':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusLabel(String status) {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Test Request'),
        content: const Text('Are you sure you want to delete this test request? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement delete functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Test request deleted')),
              );
              context.pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _downloadReport() {
    // TODO: Implement report download
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Downloading report...')),
    );
  }
}
