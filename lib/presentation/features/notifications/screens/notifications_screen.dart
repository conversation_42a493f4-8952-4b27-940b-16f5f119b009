import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  String _filterType = 'all';

  // Mock data - will be replaced with actual data from BLoC
  final List<Map<String, dynamic>> _mockNotifications = [
    {
      'id': 1,
      'type': 'job_assigned',
      'title': 'New Job Assigned',
      'message': 'You have been assigned job JOB-2024-001 for water testing',
      'timestamp': '2024-01-22 10:30:00',
      'isRead': false,
      'priority': 'high',
      'relatedId': 1,
      'relatedType': 'job',
      'icon': Icons.work,
      'color': Colors.blue,
    },
    {
      'id': 2,
      'type': 'test_approved',
      'title': 'Test Results Approved',
      'message': 'Your test results for TR-2024-002 have been approved by Chief <PERSON><PERSON><PERSON>',
      'timestamp': '2024-01-22 09:15:00',
      'isRead': true,
      'priority': 'medium',
      'relatedId': 2,
      'relatedType': 'test_request',
      'icon': Icons.check_circle,
      'color': Colors.green,
    },
    {
      'id': 3,
      'type': 'test_rejected',
      'title': 'Test Results Rejected',
      'message': 'Your test results for TR-2024-001 have been rejected. Reason: Incomplete analysis',
      'timestamp': '2024-01-21 16:45:00',
      'isRead': false,
      'priority': 'high',
      'relatedId': 1,
      'relatedType': 'test_request',
      'icon': Icons.cancel,
      'color': Colors.red,
    },
    {
      'id': 4,
      'type': 'due_date_reminder',
      'title': 'Due Date Reminder',
      'message': 'Job JOB-2024-003 is due tomorrow. Please complete the analysis.',
      'timestamp': '2024-01-21 14:20:00',
      'isRead': true,
      'priority': 'medium',
      'relatedId': 3,
      'relatedType': 'job',
      'icon': Icons.schedule,
      'color': Colors.orange,
    },
    {
      'id': 5,
      'type': 'qc_assigned',
      'title': 'Quality Control Test Assigned',
      'message': 'You have been assigned a blind test for parameter pH Level',
      'timestamp': '2024-01-21 11:30:00',
      'isRead': false,
      'priority': 'medium',
      'relatedId': 5,
      'relatedType': 'qc_test',
      'icon': Icons.verified,
      'color': Colors.purple,
    },
    {
      'id': 6,
      'type': 'system_maintenance',
      'title': 'System Maintenance',
      'message': 'Scheduled maintenance will occur on Jan 25, 2024 from 2:00 AM to 4:00 AM',
      'timestamp': '2024-01-20 18:00:00',
      'isRead': true,
      'priority': 'low',
      'relatedId': null,
      'relatedType': 'system',
      'icon': Icons.build,
      'color': Colors.grey,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredNotifications = _getFilteredNotifications();
    final unreadCount = _mockNotifications.where((n) => !n['isRead']).length;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Text('Notifications'),
            if (unreadCount > 0) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$unreadCount',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          if (unreadCount > 0)
            TextButton(
              onPressed: _markAllAsRead,
              child: const Text('Mark All Read'),
            ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'clear_read':
                  _clearReadNotifications();
                  break;
                case 'settings':
                  _showNotificationSettings();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear_read',
                child: ListTile(
                  leading: Icon(Icons.clear),
                  title: Text('Clear Read'),
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('Settings'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Tabs
          Container(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('all', 'All', Icons.notifications),
                  const SizedBox(width: 8),
                  _buildFilterChip('job_assigned', 'Jobs', Icons.work),
                  const SizedBox(width: 8),
                  _buildFilterChip('test_approved', 'Approvals', Icons.check_circle),
                  const SizedBox(width: 8),
                  _buildFilterChip('test_rejected', 'Rejections', Icons.cancel),
                  const SizedBox(width: 8),
                  _buildFilterChip('qc_assigned', 'QC Tests', Icons.verified),
                  const SizedBox(width: 8),
                  _buildFilterChip('due_date_reminder', 'Reminders', Icons.schedule),
                ],
              ),
            ),
          ),

          // Notifications List
          Expanded(
            child: filteredNotifications.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    itemCount: filteredNotifications.length,
                    itemBuilder: (context, index) {
                      final notification = filteredNotifications[index];
                      return _buildNotificationCard(notification);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String type, String label, IconData icon) {
    final isSelected = _filterType == type;
    final count = type == 'all'
        ? _mockNotifications.length
        : _mockNotifications.where((n) => n['type'] == type).length;

    return FilterChip(
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _filterType = type;
        });
      },
      avatar: Icon(
        icon,
        size: 16,
        color: isSelected ? Colors.white : Colors.grey[600],
      ),
      label: Text(
        '$label ($count)',
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.grey[600],
          fontSize: 12,
        ),
      ),
      selectedColor: Theme.of(context).primaryColor,
      backgroundColor: Colors.grey[200],
    );
  }

  Widget _buildNotificationCard(Map<String, dynamic> notification) {
    final isRead = notification['isRead'] as bool;
    final timestamp = DateTime.parse(notification['timestamp']);
    final timeAgo = _getTimeAgo(timestamp);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _handleNotificationTap(notification),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: !isRead ? Border.all(color: Colors.blue, width: 2) : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: notification['color'].withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        notification['icon'],
                        color: notification['color'],
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  notification['title'],
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: isRead ? FontWeight.w500 : FontWeight.bold,
                                  ),
                                ),
                              ),
                              Row(
                                children: [
                                  _buildPriorityIndicator(notification['priority']),
                                  const SizedBox(width: 8),
                                  if (!isRead)
                                    Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        color: Colors.blue,
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            notification['message'],
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                timeAgo,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[500],
                                ),
                              ),
                              if (!isRead)
                                TextButton(
                                  onPressed: () => _markAsRead(notification),
                                  child: const Text(
                                    'Mark as read',
                                    style: TextStyle(fontSize: 12),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityIndicator(String priority) {
    Color color;
    switch (priority) {
      case 'high':
        color = Colors.red;
        break;
      case 'medium':
        color = Colors.orange;
        break;
      case 'low':
        color = Colors.green;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No notifications',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'re all caught up!',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredNotifications() {
    if (_filterType == 'all') {
      return _mockNotifications;
    }
    return _mockNotifications.where((n) => n['type'] == _filterType).toList();
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _handleNotificationTap(Map<String, dynamic> notification) {
    // Mark as read when tapped
    if (!notification['isRead']) {
      _markAsRead(notification);
    }

    // Navigate based on notification type
    final relatedType = notification['relatedType'];
    final relatedId = notification['relatedId'];

    if (relatedId != null) {
      switch (relatedType) {
        case 'job':
          context.push('/job-details/$relatedId');
          break;
        case 'test_request':
          context.push('/test-details/$relatedId');
          break;
        case 'qc_test':
          context.push('/quality-control');
          break;
        default:
          // Handle other types or show details
          break;
      }
    }
  }

  void _markAsRead(Map<String, dynamic> notification) {
    setState(() {
      notification['isRead'] = true;
    });

    // TODO: Update read status via BLoC
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Notification marked as read'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _markAllAsRead() {
    setState(() {
      for (var notification in _mockNotifications) {
        notification['isRead'] = true;
      }
    });

    // TODO: Update all read status via BLoC
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('All notifications marked as read'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _clearReadNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Read Notifications'),
        content: const Text('Are you sure you want to clear all read notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _mockNotifications.removeWhere((n) => n['isRead'] == true);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Read notifications cleared')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Settings'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: Text('Job Assignments'),
              value: true,
              onChanged: null,
            ),
            CheckboxListTile(
              title: Text('Test Approvals'),
              value: true,
              onChanged: null,
            ),
            CheckboxListTile(
              title: Text('Due Date Reminders'),
              value: true,
              onChanged: null,
            ),
            CheckboxListTile(
              title: Text('QC Test Assignments'),
              value: false,
              onChanged: null,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}