import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../core/router/app_routes.dart';

class QualityControlScreen extends StatefulWidget {
  const QualityControlScreen({super.key});

  @override
  State<QualityControlScreen> createState() => _QualityControlScreenState();
}

class _QualityControlScreenState extends State<QualityControlScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _qcTypeFilter = 'all';

  // Mock data - will be replaced with actual data from BLoC
  final List<Map<String, dynamic>> _mockQcTests = [
    {
      'id': 1,
      'originalTestId': 1,
      'requestNumber': 'TR-2024-001',
      'customerName': 'ABC Industries',
      'originalAnalyst': '<PERSON>lyst',
      'originalResult': '7.2 pH',
      'parameter': 'pH Level',
      'qcType': 'retest',
      'status': 'pending',
      'createdDate': '2024-01-22',
      'reason': 'Customer complaint about result accuracy',
      'priority': 'high',
    },
    {
      'id': 2,
      'originalTestId': 2,
      'requestNumber': 'TR-2024-002',
      'customerName': 'XYZ Corp',
      'originalAnalyst': 'Jane Analyst',
      'originalResult': '1.2 mg/L',
      'parameter': 'Chlorine Content',
      'qcType': 'blind',
      'status': 'in_progress',
      'assignedAnalyst': 'Mike Technician',
      'createdDate': '2024-01-21',
      'reason': 'Random quality check',
      'priority': 'medium',
    },
    {
      'id': 3,
      'originalTestId': 3,
      'requestNumber': 'TR-2024-003',
      'customerName': 'DEF Ltd',
      'originalAnalyst': 'John Analyst',
      'originalResult': '2.1 NTU',
      'parameter': 'Turbidity',
      'qcType': 'replicate',
      'status': 'completed',
      'assignedAnalyst': 'John Analyst',
      'replicateResult': '2.0 NTU',
      'createdDate': '2024-01-20',
      'completedDate': '2024-01-21',
      'reason': 'Precision verification',
      'priority': 'low',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final filteredTests = _getFilteredTests();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Quality Control'),
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showQcStatistics,
          ),
        ],
      ),
      body: Column(
        children: [
          // QC Type Cards
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(child: _buildQcTypeCard('Retest', Icons.refresh, Colors.blue)),
                const SizedBox(width: 8),
                Expanded(child: _buildQcTypeCard('Blind', Icons.visibility_off, Colors.orange)),
                const SizedBox(width: 8),
                Expanded(child: _buildQcTypeCard('Replicate', Icons.copy, Colors.green)),
              ],
            ),
          ),

          // Search and Filter Section
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search by request number or customer...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 12),
                
                // QC Type Filter
                Row(
                  children: [
                    const Text('QC Type: '),
                    const SizedBox(width: 8),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _qcTypeFilter,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('All Types')),
                          DropdownMenuItem(value: 'retest', child: Text('Retest')),
                          DropdownMenuItem(value: 'blind', child: Text('Blind Test')),
                          DropdownMenuItem(value: 'replicate', child: Text('Replicate')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _qcTypeFilter = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          
          // QC Tests List
          Expanded(
            child: filteredTests.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    itemCount: filteredTests.length,
                    itemBuilder: (context, index) {
                      final test = filteredTests[index];
                      return _buildQcTestCard(test);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildQcTypeCard(String title, IconData icon, Color color) {
    final count = _mockQcTests.where((test) => 
        test['qcType'] == title.toLowerCase() && test['status'] != 'completed').length;
    
    return Card(
      child: InkWell(
        onTap: () {
          setState(() {
            _qcTypeFilter = title.toLowerCase();
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '$count pending',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQcTestCard(Map<String, dynamic> test) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          _navigateToQcDetails(test);
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    test['requestNumber'],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      _buildQcTypeChip(test['qcType']),
                      const SizedBox(width: 8),
                      _buildStatusChip(test['status']),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                test['customerName'],
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.science, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Parameter: ${test['parameter']}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Original Analyst: ${test['originalAnalyst']}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              if (test['assignedAnalyst'] != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.assignment_ind, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Assigned to: ${test['assignedAnalyst']}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Original Result: ${test['originalResult']}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (test['replicateResult'] != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'QC Result: ${test['replicateResult']}',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Reason: ${test['reason']}',
                style: const TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
              
              // Action buttons for pending tests
              if (test['status'] == 'pending') ...[
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    OutlinedButton.icon(
                      onPressed: () => _editQcTest(test),
                      icon: const Icon(Icons.edit, size: 16),
                      label: const Text('Edit'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: () => _assignQcTest(test),
                      icon: const Icon(Icons.person_add, size: 16),
                      label: const Text('Assign'),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQcTypeChip(String qcType) {
    Color color;
    IconData icon;
    String label;
    
    switch (qcType) {
      case 'retest':
        color = Colors.blue;
        icon = Icons.refresh;
        label = 'Retest';
        break;
      case 'blind':
        color = Colors.orange;
        icon = Icons.visibility_off;
        label = 'Blind';
        break;
      case 'replicate':
        color = Colors.green;
        icon = Icons.copy;
        label = 'Replicate';
        break;
      default:
        color = Colors.grey;
        icon = Icons.help;
        label = 'Unknown';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;
    
    switch (status) {
      case 'pending':
        color = Colors.orange;
        label = 'Pending';
        break;
      case 'in_progress':
        color = Colors.blue;
        label = 'In Progress';
        break;
      case 'completed':
        color = Colors.green;
        label = 'Completed';
        break;
      default:
        color = Colors.grey;
        label = 'Unknown';
    }

    return Chip(
      label: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      ),
      backgroundColor: color,
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.verified_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No QC tests found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Quality control tests will appear here',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredTests() {
    return _mockQcTests.where((test) {
      final matchesSearch = _searchQuery.isEmpty ||
          test['requestNumber'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
          test['customerName'].toLowerCase().contains(_searchQuery.toLowerCase());
      
      final matchesType = _qcTypeFilter == 'all' || test['qcType'] == _qcTypeFilter;
      
      return matchesSearch && matchesType;
    }).toList();
  }

  void _navigateToQcDetails(Map<String, dynamic> test) {
    switch (test['qcType']) {
      case 'retest':
        context.push(AppRoutes.retestWithId(test['originalTestId']));
        break;
      case 'blind':
        context.push(AppRoutes.blindTestWithId(test['originalTestId']));
        break;
      case 'replicate':
        context.push(AppRoutes.replicateTestWithId(test['originalTestId']));
        break;
    }
  }

  void _showQcStatistics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('QC Statistics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow('Total QC Tests', '${_mockQcTests.length}'),
            _buildStatRow('Pending', '${_mockQcTests.where((t) => t['status'] == 'pending').length}'),
            _buildStatRow('In Progress', '${_mockQcTests.where((t) => t['status'] == 'in_progress').length}'),
            _buildStatRow('Completed', '${_mockQcTests.where((t) => t['status'] == 'completed').length}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _editQcTest(Map<String, dynamic> test) {
    // TODO: Navigate to edit QC test screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Editing QC test ${test['requestNumber']}')),
    );
  }

  void _assignQcTest(Map<String, dynamic> test) {
    // TODO: Show analyst assignment dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Assigning QC test ${test['requestNumber']}')),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
