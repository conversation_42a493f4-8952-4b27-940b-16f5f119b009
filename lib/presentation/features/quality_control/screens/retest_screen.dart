import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/widgets/error_text_field.dart';
import '../../../../core/error/failures.dart';

class RetestScreen extends StatefulWidget {
  final int originalTestId;

  const RetestScreen({
    super.key,
    required this.originalTestId,
  });

  @override
  State<RetestScreen> createState() => _RetestScreenState();
}

class _RetestScreenState extends State<RetestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();
  final _instructionsController = TextEditingController();
  final _dueDateController = TextEditingController();
  
  String? _selectedAnalyst;
  String _selectedPriority = 'medium';
  DateTime? _dueDate;
  bool _isLoading = false;
  bool _dataLoading = true;
  Failure? _retestFailure;
  
  late Map<String, dynamic> _originalTest;
  final List<Map<String, dynamic>> _availableAnalysts = [
    {'id': 1, 'name': '<PERSON>lyst', 'activeJobs': 2},
    {'id': 2, 'name': '<PERSON>', 'activeJobs': 1},
    {'id': 3, 'name': 'Mike Technician', 'activeJobs': 3},
  ];

  @override
  void initState() {
    super.initState();
    _loadOriginalTest();
  }

  void _loadOriginalTest() {
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _originalTest = {
          'id': widget.originalTestId,
          'requestNumber': 'TR-2024-001',
          'customerName': 'ABC Industries',
          'originalAnalyst': 'John Analyst',
          'completedDate': '2024-01-20',
          'parameter': 'pH Level',
          'originalResult': '7.2 pH',
          'permissibleLimit': '6.5-8.5 pH',
          'method': 'Electrometric',
          'sampleType': 'Water',
          'sampleCode': 'WTR-001',
        };
        _dataLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_dataLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Create Retest'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Retest'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _createRetest,
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Create'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Error Summary
              ValidationErrorSummary(failure: _retestFailure),

              // Original Test Information
              _buildOriginalTestCard(),
              const SizedBox(height: 16),

              // Retest Details
              _buildRetestDetailsCard(),
              const SizedBox(height: 16),

              // Assignment Details
              _buildAssignmentCard(),
              const SizedBox(height: 24),

              // Create Button
              ElevatedButton(
                onPressed: _isLoading ? null : _createRetest,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text(
                        'Create Retest',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOriginalTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.history, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Original Test Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Request Number', _originalTest['requestNumber']),
            _buildInfoRow('Customer', _originalTest['customerName']),
            _buildInfoRow('Parameter', _originalTest['parameter']),
            _buildInfoRow('Sample Type', _originalTest['sampleType']),
            _buildInfoRow('Sample Code', _originalTest['sampleCode']),
            _buildInfoRow('Original Analyst', _originalTest['originalAnalyst']),
            _buildInfoRow('Completed Date', _originalTest['completedDate']),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                border: Border.all(color: Colors.blue.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Original Result',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Result: ${_originalTest['originalResult']}',
                    style: const TextStyle(fontSize: 14),
                  ),
                  Text(
                    'Limit: ${_originalTest['permissibleLimit']}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  Text(
                    'Method: ${_originalTest['method']}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRetestDetailsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.refresh, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Retest Details',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Reason for Retest
            ErrorTextField(
              fieldName: 'reason',
              labelText: 'Reason for Retest *',
              controller: _reasonController,
              failure: _retestFailure,
              maxLines: 3,
              prefixIcon: const Icon(Icons.description),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please provide a reason for retest';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Special Instructions
            ErrorTextField(
              fieldName: 'instructions',
              labelText: 'Special Instructions (Optional)',
              controller: _instructionsController,
              failure: _retestFailure,
              maxLines: 3,
              prefixIcon: const Icon(Icons.note),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignmentCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.assignment_ind, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Assignment Details',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Analyst Selection
            DropdownButtonFormField<String>(
              value: _selectedAnalyst,
              decoration: const InputDecoration(
                labelText: 'Assign to Analyst *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              items: _availableAnalysts.map((analyst) {
                return DropdownMenuItem<String>(
                  value: analyst['name'],
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 12,
                        child: Text(
                          analyst['name'].split(' ').map((n) => n[0]).join(),
                          style: const TextStyle(fontSize: 10),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(analyst['name']),
                            Text(
                              '${analyst['activeJobs']} active jobs',
                              style: const TextStyle(fontSize: 12, color: Colors.grey),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedAnalyst = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select an analyst';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Priority Selection
            DropdownButtonFormField<String>(
              value: _selectedPriority,
              decoration: const InputDecoration(
                labelText: 'Priority',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.flag),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'low',
                  child: Row(
                    children: [
                      Icon(Icons.keyboard_arrow_down, color: Colors.green),
                      SizedBox(width: 8),
                      Text('Low Priority'),
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: 'medium',
                  child: Row(
                    children: [
                      Icon(Icons.remove, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('Medium Priority'),
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: 'high',
                  child: Row(
                    children: [
                      Icon(Icons.keyboard_arrow_up, color: Colors.red),
                      SizedBox(width: 8),
                      Text('High Priority'),
                    ],
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedPriority = value!;
                });
              },
            ),
            const SizedBox(height: 16),

            // Due Date
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Due Date *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.calendar_today),
                suffixIcon: Icon(Icons.arrow_drop_down),
              ),
              readOnly: true,
              onTap: _selectDueDate,
              controller: _dueDateController,
              validator: (value) {
                if (_dueDate == null) {
                  return 'Please select a due date';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  void _selectDueDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _dueDate = picked;
        _dueDateController.text = '${picked.day}/${picked.month}/${picked.year}';
      });
    }
  }

  void _createRetest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _retestFailure = null;
    });

    try {
      // TODO: Implement actual retest creation with BLoC
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Retest created successfully')),
        );
        context.pop();
      }
    } catch (e) {
      // TODO: Handle actual creation errors
      setState(() {
        _retestFailure = const ValidationFailure(
          message: 'Failed to create retest',
          fieldErrors: {
            'reason': ['Reason must be more detailed'],
          },
        );
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _reasonController.dispose();
    _instructionsController.dispose();
    _dueDateController.dispose();
    super.dispose();
  }
}
