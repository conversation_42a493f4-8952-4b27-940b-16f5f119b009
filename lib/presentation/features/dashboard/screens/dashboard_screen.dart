import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

enum UserRole {
  master,
  frontDes<PERSON>,
  chiefChemist,
  analyst,
}

class DashboardScreen extends StatelessWidget {
  final UserRole userRole;

  const DashboardScreen({
    super.key,
    required this.userRole,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('LIMS Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              context.push('/notifications');
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.go('/login');
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildRoleSpecificOptions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleSpecificOptions(BuildContext context) {
    switch (userRole) {
      case UserRole.master:
        return _buildMasterOptions(context);
      case UserRole.frontDesk:
        return _buildFrontDeskOptions(context);
      case UserRole.chiefChemist:
        return _buildChiefChemistOptions(context);
      case UserRole.analyst:
        return _buildAnalystOptions(context);
    }
  }

  Widget _buildMasterOptions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Master Dashboard',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        // Front Desk Section
        _buildSectionTitle('Front Desk Options'),
        _buildOptionCard(
          context,
          'View Tests',
          Icons.visibility,
          () {
            // TODO: Navigate to view tests
          },
        ),
        _buildOptionCard(
          context,
          'Create Test',
          Icons.add_circle,
          () {
            context.push('/create-test');
          },
        ),
        _buildOptionCard(
          context,
          'View Reports',
          Icons.description,
          () {
            // TODO: Navigate to view reports
          },
        ),
        const SizedBox(height: 24),

        // Chief Chemist Section
        _buildSectionTitle('Chief Chemist Options'),
        _buildOptionCard(
          context,
          'Job Allocation',
          Icons.assignment,
          () {
            context.push('/job-allocation');
          },
        ),
        _buildOptionCard(
          context,
          'Approvals',
          Icons.approval,
          () {
            // TODO: Navigate to approvals
          },
        ),
        _buildOptionCard(
          context,
          'Quality Control',
          Icons.science,
          () {
            // TODO: Navigate to quality control
          },
        ),
        const SizedBox(height: 24),

        // Analyst Section
        _buildSectionTitle('Analyst Options'),
        _buildOptionCard(
          context,
          'View Jobs',
          Icons.work,
          () {
            context.push('/analyst-jobs');
          },
        ),
        const SizedBox(height: 24),

        // System Options
        _buildSectionTitle('System Options'),
        _buildOptionCard(
          context,
          'User Management',
          Icons.people,
          () {
            // TODO: Navigate to user management
          },
        ),
        _buildOptionCard(
          context,
          'System Settings',
          Icons.settings,
          () {
            // TODO: Navigate to system settings
          },
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildFrontDeskOptions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Front Desk Options',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildOptionCard(
          context,
          'View Tests',
          Icons.visibility,
          () {
            // TODO: Navigate to view tests
          },
        ),
        _buildOptionCard(
          context,
          'Create Test',
          Icons.add_circle,
          () {
            context.push('/create-test');
          },
        ),
        _buildOptionCard(
          context,
          'View Reports',
          Icons.description,
          () {
            // TODO: Navigate to view reports
          },
        ),
      ],
    );
  }

  Widget _buildChiefChemistOptions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Chief Chemist Options',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildOptionCard(
          context,
          'Job Allocation',
          Icons.assignment,
          () {
            context.push('/job-allocation');
          },
        ),
        _buildOptionCard(
          context,
          'Approvals',
          Icons.approval,
          () {
            // TODO: Navigate to approvals
          },
        ),
        _buildOptionCard(
          context,
          'Quality Control',
          Icons.science,
          () {
            // TODO: Navigate to quality control
          },
        ),
        // Include Front Desk options
        const SizedBox(height: 24),
        const Text(
          'Front Desk Options',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildOptionCard(
          context,
          'View Tests',
          Icons.visibility,
          () {
            // TODO: Navigate to view tests
          },
        ),
        _buildOptionCard(
          context,
          'Create Test',
          Icons.add_circle,
          () {
            context.push('/create-test');
          },
        ),
        _buildOptionCard(
          context,
          'View Reports',
          Icons.description,
          () {
            // TODO: Navigate to view reports
          },
        ),
      ],
    );
  }

  Widget _buildAnalystOptions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Analyst Options',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildOptionCard(
          context,
          'View Jobs',
          Icons.work,
          () {
            context.push('/analyst-jobs');
          },
        ),
      ],
    );
  }

  Widget _buildOptionCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        leading: Icon(icon, size: 32),
        title: Text(
          title,
          style: const TextStyle(fontSize: 18),
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }
} 