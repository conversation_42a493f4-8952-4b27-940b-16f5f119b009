import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../../core/network/api_response.dart';
import '../models/auth/login_request_model.dart';
import '../models/auth/login_response_model.dart';
import '../models/auth/user_model.dart';
import '../models/auth/update_profile_request_model.dart';

abstract class AuthRemoteDataSource {
  Future<LoginResponseModel> login(LoginRequestModel request);
  Future<UserModel> getProfile();
  Future<UserModel> updateProfile(UpdateProfileRequestModel request);
  Future<void> logout();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final DioClient _dioClient;

  AuthRemoteDataSourceImpl(this._dioClient);

  @override
  Future<LoginResponseModel> login(LoginRequestModel request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.login,
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return LoginResponseModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Login failed',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Login failed',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<UserModel> getProfile() async {
    try {
      final response = await _dioClient.dio.get(ApiConstants.profile);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return UserModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get profile',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get profile',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<UserModel> updateProfile(UpdateProfileRequestModel request) async {
    try {
      final response = await _dioClient.dio.put(
        ApiConstants.profile,
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return UserModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to update profile',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to update profile',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<void> logout() async {
    try {
      final response = await _dioClient.dio.post(ApiConstants.logout);

      if (response.statusCode != 200) {
        throw ServerException(
          message: 'Logout failed',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
