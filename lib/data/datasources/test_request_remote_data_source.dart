import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/dio_client.dart';
import '../../core/network/api_response.dart';
import '../models/test_request/test_request_model.dart';
import '../models/test_request/sample_model.dart';
import '../models/common/test_requests_response_model.dart';
import '../models/common/samples_response_model.dart';

abstract class TestRequestRemoteDataSource {
  Future<TestRequestsResponseData> getTestRequests({
    int page = 1,
    int perPage = 15,
    String? search,
  });
  Future<TestRequestModel> getTestRequestById(int id);
  Future<TestRequestModel> createTestRequest(TestRequestModel request);
  Future<TestRequestModel> updateTestRequest(int id, TestRequestModel request);
  Future<void> deleteTestRequest(int id);
  Future<List<SampleModel>> getSamples(int testRequestId);
  Future<SampleModel> addSample(int testRequestId, SampleModel sample);
}

class TestRequestRemoteDataSourceImpl implements TestRequestRemoteDataSource {
  final DioClient _dioClient;

  TestRequestRemoteDataSourceImpl(this._dioClient);

  @override
  Future<TestRequestsResponseData> getTestRequests({
    int page = 1,
    int perPage = 15,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      final response = await _dioClient.dio.get(
        ApiConstants.testRequests,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final apiResponse = TestRequestsApiResponse.fromJson(
          response.data,
          (json) => TestRequestsResponseData.fromJson(json as Map<String, dynamic>),
        );

        if (apiResponse.success && apiResponse.data != null) {
          return apiResponse.data!;
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get test requests',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get test requests',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestRequestModel> getTestRequestById(int id) async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.testRequestById(id),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return TestRequestModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get test request',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get test request',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestRequestModel> createTestRequest(TestRequestModel request) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.testRequests,
        data: request.toJson(),
      );

      if (response.statusCode == 201) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return TestRequestModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to create test request',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to create test request',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<TestRequestModel> updateTestRequest(int id, TestRequestModel request) async {
    try {
      final response = await _dioClient.dio.put(
        ApiConstants.testRequestById(id),
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return TestRequestModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to update test request',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to update test request',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<void> deleteTestRequest(int id) async {
    try {
      final response = await _dioClient.dio.delete(
        ApiConstants.testRequestById(id),
      );

      if (response.statusCode != 200) {
        throw ServerException(
          message: 'Failed to delete test request',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<List<SampleModel>> getSamples(int testRequestId) async {
    try {
      final response = await _dioClient.dio.get(
        ApiConstants.testRequestSamples(testRequestId),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return apiResponse.data!
              .map((json) => SampleModel.fromJson(json as Map<String, dynamic>))
              .toList();
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to get samples',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to get samples',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }

  @override
  Future<SampleModel> addSample(int testRequestId, SampleModel sample) async {
    try {
      final response = await _dioClient.dio.post(
        ApiConstants.testRequestSamples(testRequestId),
        data: sample.toJson(),
      );

      if (response.statusCode == 201) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.success && apiResponse.data != null) {
          return SampleModel.fromJson(apiResponse.data!);
        } else {
          throw ServerException(
            message: apiResponse.message ?? 'Failed to add sample',
            statusCode: response.statusCode,
          );
        }
      } else {
        throw ServerException(
          message: 'Failed to add sample',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      if (e.error is Exception) {
        throw e.error as Exception;
      }
      throw NetworkException(message: e.message ?? 'Network error occurred');
    } catch (e) {
      throw ServerException(message: 'An unexpected error occurred');
    }
  }
}
