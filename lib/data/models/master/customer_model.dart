import 'package:json_annotation/json_annotation.dart';

part 'customer_model.g.dart';

@JsonSerializable()
class CustomerModel {
  final int? id;
  final String name;
  final String address;
  @Json<PERSON><PERSON>(name: 'contact_person')
  final String contactPerson;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'mobile_no')
  final String mobileNo;
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final String? updatedAt;

  const CustomerModel({
    this.id,
    required this.name,
    required this.address,
    required this.contactPerson,
    required this.mobileNo,
    required this.email,
    this.createdAt,
    this.updatedAt,
  });

  factory CustomerModel.fromJson(Map<String, dynamic> json) =>
      _$CustomerModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerModelToJson(this);
}
