// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'job_allocation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JobAllocationModel _$JobAllocationModelFromJson(Map<String, dynamic> json) =>
    JobAllocationModel(
      id: (json['id'] as num?)?.toInt(),
      serialNo: json['serial_no'] as String,
      creationDate: json['creation_date'] as String,
      codeNumber: json['code_number'] as String,
      nature: json['nature'] as String,
      quantity: json['quantity'] as String,
      collectionDate: json['collection_date'] as String,
      submissionDate: json['submission_date'] as String,
      dueDate: json['due_date'] as String,
      userId: (json['user_id'] as num).toInt(),
      testRequestId: (json['test_request_id'] as num).toInt(),
      reportType: json['report_type'] as String,
      designation: json['designation'] as String,
      remarks: json['remarks'] as String?,
      nablStatus: json['nabl_status'] as String,
      deleted: (json['deleted'] as num?)?.toInt() ?? 0,
      testRequest: json['test_request'] == null
          ? null
          : TestRequestModel.fromJson(
              json['test_request'] as Map<String, dynamic>,
            ),
      user: json['user'] == null
          ? null
          : UserModel.fromJson(json['user'] as Map<String, dynamic>),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$JobAllocationModelToJson(JobAllocationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'serial_no': instance.serialNo,
      'creation_date': instance.creationDate,
      'code_number': instance.codeNumber,
      'nature': instance.nature,
      'quantity': instance.quantity,
      'collection_date': instance.collectionDate,
      'submission_date': instance.submissionDate,
      'due_date': instance.dueDate,
      'user_id': instance.userId,
      'test_request_id': instance.testRequestId,
      'report_type': instance.reportType,
      'designation': instance.designation,
      'remarks': instance.remarks,
      'nabl_status': instance.nablStatus,
      'deleted': instance.deleted,
      'test_request': instance.testRequest,
      'user': instance.user,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
