import 'package:json_annotation/json_annotation.dart';
import '../test_request/test_request_model.dart';
import '../auth/user_model.dart';

part 'job_allocation_model.g.dart';

@JsonSerializable()
class JobAllocationModel {
  final int? id;
  @J<PERSON><PERSON><PERSON>(name: 'serial_no')
  final String serialNo;
  @Json<PERSON><PERSON>(name: 'creation_date')
  final String creationDate;
  @Json<PERSON>ey(name: 'code_number')
  final String codeNumber;
  final String nature;
  final String quantity;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'collection_date')
  final String collectionDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'submission_date')
  final String submissionDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'due_date')
  final String dueDate;
  @J<PERSON><PERSON>ey(name: 'user_id')
  final int userId;
  @Json<PERSON>ey(name: 'test_request_id')
  final int testRequestId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'report_type')
  final String reportType;
  final String designation;
  final String? remarks;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'nabl_status')
  final String nablStatus;
  final int deleted;
  @J<PERSON><PERSON><PERSON>(name: 'test_request')
  final TestRequestModel? testRequest; // Nested test request object
  final UserModel? user; // Nested user object
  @Json<PERSON>ey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  const JobAllocationModel({
    this.id,
    required this.serialNo,
    required this.creationDate,
    required this.codeNumber,
    required this.nature,
    required this.quantity,
    required this.collectionDate,
    required this.submissionDate,
    required this.dueDate,
    required this.userId,
    required this.testRequestId,
    required this.reportType,
    required this.designation,
    this.remarks,
    required this.nablStatus,
    this.deleted = 0,
    this.testRequest,
    this.user,
    this.createdAt,
    this.updatedAt,
  });

  factory JobAllocationModel.fromJson(Map<String, dynamic> json) =>
      _$JobAllocationModelFromJson(json);

  Map<String, dynamic> toJson() => _$JobAllocationModelToJson(this);
}
