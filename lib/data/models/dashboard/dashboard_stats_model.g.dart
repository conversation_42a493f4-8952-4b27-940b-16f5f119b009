// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_stats_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TotalStatsModel _$TotalStatsModelFromJson(Map<String, dynamic> json) =>
    TotalStatsModel(
      totalTestRequests: (json['total_test_requests'] as num).toInt(),
      totalJobAllocations: (json['total_job_allocations'] as num).toInt(),
      totalUsers: (json['total_users'] as num).toInt(),
    );

Map<String, dynamic> _$TotalStatsModelToJson(TotalStatsModel instance) =>
    <String, dynamic>{
      'total_test_requests': instance.totalTestRequests,
      'total_job_allocations': instance.totalJobAllocations,
      'total_users': instance.totalUsers,
    };

MonthlyStatsModel _$MonthlyStatsModelFromJson(Map<String, dynamic> json) =>
    MonthlyStatsModel(
      monthlyTestRequests: (json['monthly_test_requests'] as num).toInt(),
      monthlyJobAllocations: (json['monthly_job_allocations'] as num).toInt(),
    );

Map<String, dynamic> _$MonthlyStatsModelToJson(MonthlyStatsModel instance) =>
    <String, dynamic>{
      'monthly_test_requests': instance.monthlyTestRequests,
      'monthly_job_allocations': instance.monthlyJobAllocations,
    };

TaskStatsModel _$TaskStatsModelFromJson(Map<String, dynamic> json) =>
    TaskStatsModel(
      overdueTasks: (json['overdue_tasks'] as num).toInt(),
      todayTasks: (json['today_tasks'] as num).toInt(),
      upcomingTasks: (json['upcoming_tasks'] as num).toInt(),
    );

Map<String, dynamic> _$TaskStatsModelToJson(TaskStatsModel instance) =>
    <String, dynamic>{
      'overdue_tasks': instance.overdueTasks,
      'today_tasks': instance.todayTasks,
      'upcoming_tasks': instance.upcomingTasks,
    };

UserStatsModel _$UserStatsModelFromJson(Map<String, dynamic> json) =>
    UserStatsModel(
      myJobs: (json['my_jobs'] as num).toInt(),
      myOverdueTasks: (json['my_overdue_tasks'] as num).toInt(),
    );

Map<String, dynamic> _$UserStatsModelToJson(UserStatsModel instance) =>
    <String, dynamic>{
      'my_jobs': instance.myJobs,
      'my_overdue_tasks': instance.myOverdueTasks,
    };

DashboardStatsModel _$DashboardStatsModelFromJson(Map<String, dynamic> json) =>
    DashboardStatsModel(
      totalStats: TotalStatsModel.fromJson(
        json['total_stats'] as Map<String, dynamic>,
      ),
      monthlyStats: MonthlyStatsModel.fromJson(
        json['monthly_stats'] as Map<String, dynamic>,
      ),
      taskStats: TaskStatsModel.fromJson(
        json['task_stats'] as Map<String, dynamic>,
      ),
      userStats: UserStatsModel.fromJson(
        json['user_stats'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$DashboardStatsModelToJson(
  DashboardStatsModel instance,
) => <String, dynamic>{
  'total_stats': instance.totalStats,
  'monthly_stats': instance.monthlyStats,
  'task_stats': instance.taskStats,
  'user_stats': instance.userStats,
};
