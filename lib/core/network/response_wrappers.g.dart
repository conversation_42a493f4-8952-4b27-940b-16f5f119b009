// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'response_wrappers.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaginatedListResponse<T> _$PaginatedListResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => PaginatedListResponse<T>(
  items: (json['items'] as List<dynamic>).map(fromJsonT).toList(),
  pagination: PaginationMeta.fromJson(
    json['pagination'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$PaginatedListResponseToJson<T>(
  PaginatedListResponse<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'items': instance.items.map(toJsonT).toList(),
  'pagination': instance.pagination,
};

ListResponse<T> _$ListResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => ListResponse<T>(
  items: (json['items'] as List<dynamic>).map(fromJsonT).toList(),
);

Map<String, dynamic> _$ListResponseToJson<T>(
  ListResponse<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{'items': instance.items.map(toJsonT).toList()};

SingleItemResponse<T> _$SingleItemResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => SingleItemResponse<T>(item: fromJsonT(json['item']));

Map<String, dynamic> _$SingleItemResponseToJson<T>(
  SingleItemResponse<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{'item': toJsonT(instance.item)};
