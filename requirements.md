# LIMS Application Requirements

## Table of Contents
1. [Authentication Module](#1-authentication-module)
2. [Front Desk Module](#2-front-desk-module)
3. [Chief Chemist <PERSON>](#3-chief-chemist-module)
4. [Analyst <PERSON>](#4-analyst-module)
5. [Notifications Module](#5-notifications-module)
6. [Common Features](#6-common-features)

## 1. Authentication Module

### 1.1 Login Screen
#### Features
- **Username/Email Input**
  - Type: Text input
  - Validation: Required, Valid email format

- **Password Input**
  - Type: Password input
  - Validation: Required, Minimum 6 characters

- **Role Selection**
  - Type: Dropdown
  - Options: Front Desk, Chief Chemist, Analyst

- **Login Button**
  - Type: Action button
  - Function: Authenticate user
  - States: Enabled, Disabled (loading)

- **Forgot Password Link**
  - Type: Navigation link
  - Destination: Forgot Password Screen

- **Error Messages**
  - Type: Text display
  - States: Hidden, Visible

### 1.2 Forgot Password Screen
#### Features
- **Email Input**
  - Type: Text input
  - Validation: Required, Valid email format

- **Submit Button**
  - Type: Action button
  - Function: Send reset link
  - States: Enabled, Disabled (loading)

- **Back to Login**
  - Type: Navigation link
  - Destination: Login Screen

- **Success/Error Messages**
  - Type: Text display
  - States: Hidden, Visible

## 2. Front Desk Module

### 2.1 Test List Screen
#### Features
- **Test List Table**
  - Type: Data table
  - Columns:
    * Test ID
    * Customer name
    * Test type
    * Status
    * Created date
    * Priority

- **Search Bar**
  - Type: Text input
  - Function: Filter tests

- **Filters**
  - Type: Multiple select
  - Options:
    * Date range
    * Status
    * Test type

- **Create Test Button**
  - Type: Action button
  - Destination: Create Test Screen

- **Export to Excel**
  - Type: Action button
  - Function: Download test list

### 2.2 Create Test Screen
#### Features
- **Basic Information**
  - Type: Form fields
  - Fields:
    * Customer name
    * Sample ID
    * Test type
    * Priority
    * Collection date

- **Test Parameters**
  - Type: Dynamic form
  - Fields:
    * Parameter name
    * Expected value
    * Unit

- **Attachments**
  - Type: File upload
  - Function: Upload supporting documents

- **Submit Button**
  - Type: Action button
  - Function: Create new test

- **Save as Draft**
  - Type: Action button
  - Function: Save incomplete test

### 2.3 Reports Screen
#### Features
- **Report List**
  - Type: Data table
  - Columns:
    * Report ID
    * Test ID
    * Customer
    * Generated date
    * Status

- **Search and Filters**
  - Type: Multiple inputs
  - Function: Filter reports

- **Preview Report**
  - Type: Action button
  - Function: View report before download

- **Download PDF**
  - Type: Action button
  - Function: Download single report

- **Batch Download**
  - Type: Action button
  - Function: Download multiple reports

## 3. Chief Chemist Module

### 3.1 Job Allocation Screen
#### Features
- **Pending Tests List**
  - Type: Data table
  - Columns:
    * Test ID
    * Customer
    * Test type
    * Priority
    * Created date

- **Analyst Selection**
  - Type: Dropdown
  - Function: Assign analyst

- **Deadline Setting**
  - Type: Date picker
  - Function: Set completion deadline

- **Instructions**
  - Type: Text area
  - Function: Add special instructions

- **Assign Button**
  - Type: Action button
  - Function: Create job assignment

### 3.2 Job Management Screen
#### Features
- **Jobs List**
  - Type: Data table
  - Columns:
    * Job ID
    * Test ID
    * Analyst
    * Status
    * Deadline

- **Search and Filters**
  - Type: Multiple inputs
  - Function: Filter jobs

- **Edit Job**
  - Type: Action button
  - Function: Modify job details

- **Delete Job**
  - Type: Action button
  - Function: Remove job

- **Job History**
  - Type: Timeline view
  - Function: View job changes

### 3.3 Approvals Screen
#### Features
- **Pending Approvals List**
  - Type: Data table
  - Columns:
    * Test ID
    * Analyst
    * Submitted date
    * Status

- **Test Details View**
  - Type: Detailed view
  - Function: Review test information

- **Results Review**
  - Type: Data display
  - Function: Review test results

- **Accept/Reject Buttons**
  - Type: Action buttons
  - Function: Approve or reject test

- **Comments**
  - Type: Text area
  - Function: Add approval notes

### 3.4 Quality Control Screen
#### Features
- **Quality Control List**
  - Type: Data table
  - Columns:
    * Test ID
    * Type (Retest/Blind/Replicate)
    * Status
    * Original analyst

- **New Quality Control Button**
  - Type: Action button
  - Function: Initiate quality control

- **Filter by Type**
  - Type: Dropdown
  - Function: Filter quality control items

### 3.5 Retest Screen
#### Features
- **Original Test Details**
  - Type: Detailed view
  - Function: Review original test

- **Results Comparison**
  - Type: Comparison view
  - Function: Compare original and retest results

- **New Analyst Selection**
  - Type: Dropdown
  - Function: Assign retest

- **Instructions**
  - Type: Text area
  - Function: Add retest instructions

- **Submit Button**
  - Type: Action button
  - Function: Create retest

### 3.6 Blind Test Screen
#### Features
- **Test Details**
  - Type: Detailed view
  - Function: Review test information

- **New Analyst Selection**
  - Type: Dropdown
  - Function: Assign blind test

- **Instructions**
  - Type: Text area
  - Function: Add blind test instructions

- **Submit Button**
  - Type: Action button
  - Function: Create blind test

### 3.7 Replicate Test Screen
#### Features
- **Original Test Details**
  - Type: Detailed view
  - Function: Review original test

- **Analyst Selection**
  - Type: Radio buttons
  - Options:
    * Same analyst
    * New analyst

- **Instructions**
  - Type: Text area
  - Function: Add replicate test instructions

- **Submit Button**
  - Type: Action button
  - Function: Create replicate test

## 4. Analyst Module

### 4.1 My Jobs Screen
#### Features
- **Assigned Jobs List**
  - Type: Data table
  - Columns:
    * Job ID
    * Test ID
    * Customer
    * Deadline
    * Priority

- **Search and Filters**
  - Type: Multiple inputs
  - Function: Filter jobs

- **Job Status**
  - Type: Status indicator
  - States: Pending, In Progress, Completed

### 4.2 Fill Results Screen
#### Features
- **Test Details**
  - Type: Detailed view
  - Function: Review test information

- **Parameter Results**
  - Type: Dynamic form
  - Fields:
    * Parameter name
    * Result value
    * Unit
    * Method used

- **Attachments**
  - Type: File upload
  - Function: Upload supporting documents

- **Comments**
  - Type: Text area
  - Function: Add result notes

- **Save Draft**
  - Type: Action button
  - Function: Save incomplete results

- **Submit Results**
  - Type: Action button
  - Function: Submit completed results

### 4.3 Reject Job Screen
#### Features
- **Rejection Reason**
  - Type: Text area
  - Function: Specify rejection reason

- **Required Modifications**
  - Type: Text area
  - Function: List required changes

- **Submit Rejection**
  - Type: Action button
  - Function: Submit job rejection

## 5. Notifications Module

### 5.1 Notifications List Screen
#### Features
- **Notifications List**
  - Type: List view
  - Items:
    * Type
    * Message
    * Timestamp
    * Status (read/unread)

- **Mark as Read**
  - Type: Action button
  - Function: Update notification status

- **Clear All**
  - Type: Action button
  - Function: Clear all notifications

- **Filter by Type**
  - Type: Dropdown
  - Function: Filter notifications

## 6. Common Features

### 6.1 Profile Screen
#### Features
- **User Information**
  - Type: Display fields
  - Fields:
    * Name
    * Email
    * Role
    * Department

- **Change Password**
  - Type: Form
  - Fields:
    * Current password
    * New password
    * Confirm password

- **Notification Preferences**
  - Type: Toggle switches
  - Options:
    * Email notifications
    * In-app notifications

### 6.2 Settings Screen
#### Features
- **Theme Selection**
  - Type: Radio buttons
  - Options:
    * Light
    * Dark
    * System default

- **Language Selection**
  - Type: Dropdown
  - Function: Change application language

- **Default View Settings**
  - Type: Multiple toggles
  - Options:
    * Default date range
    * Default sort order
    * Items per page 